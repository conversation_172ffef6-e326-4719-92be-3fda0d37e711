import {
	AppShell,
	Group,
	Title,
	Text,
	Burger,
	Menu,
	Avatar,
	Box,
	MenuItem,
	Button,
	Image,
} from "@mantine/core";
import openCustomModal from "./modals/CustomModal";
import { IconInfoCircle, IconLogout, IconUser } from "@tabler/icons-react";
import { useMemo, useState } from "react";
import { APP_NAME, ProfileStatusMapping, roleValues } from "../constants";
import { isAxiosError } from "axios";
import { notifications } from "@mantine/notifications";

import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import Notifications from "./Feedbacks/Notifications";
import WelcomeModal from "./modals/WelcomeModal";

interface HeaderProps {
	opened?: boolean;
	toggle?: () => void;
	showTitle?: boolean;
}

function Header({ opened, toggle, showTitle = true }: HeaderProps) {
	const { user, logout, switchAdminPanel } = useAuth();
	const [showWelcomeModal, setShowWelcomeModal] = useState<boolean>(false);
	const navigate = useNavigate();

	const handleLogout = async () => {
		await logout();
		navigate("/login");
	};

	const canSignInAsAdmin = useMemo(() => {
		if (!user) return false;
		if (user.onboardingStepCompleted) return false;
		if (
			user.role === roleValues.SuperAdmin ||
			user.role === roleValues.Admin
		) {
			return true;
		}
		return false;
	}, [user]);

	const handleSignAsAdmin = async () => {
		if (!canSignInAsAdmin) return;
		try {
			navigate("/switching");
			const response = await switchAdminPanel();
			notifications.show({
				title: "Success",
				message:
					response?.message || "Admin panel switched successfully",
				color: "green",
			});
		} catch (error) {
			console.error("Header: Admin panel switch failed", error);
			navigate("/");
			if (isAxiosError(error)) {
				notifications.show({
					title: "Failed",
					message:
						error.response?.data?.message ??
						"Failed to sign in as admin",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to sign in as admin",
					color: "red",
				});
			}
		}
	};

	return (
		<AppShell.Header p="lg">
			<Group justify="space-between" h={"100%"}>
				<Group gap={10} justify="center">
					{toggle && (
						<Burger
							opened={opened}
							onClick={toggle}
							hiddenFrom="sm"
							size="sm"
						/>
					)}
					{showTitle && (
						<Group align="center">
							<Image
								src="/assets/SMLogoTransparent.png"
								alt="SM"
								h={60}
								w={60}
								onClick={() => navigate("/")}
								style={{ cursor: "pointer" }}
								fit="contain"
							/>
							<Title order={1} fw={600}>
								{APP_NAME}
							</Title>
						</Group>
					)}
				</Group>

				{user && (
					<Group gap={20}>
						{canSignInAsAdmin && (
							<Button
								onClick={() => {
									openCustomModal({
										title:
											`Are you sure you want ` +
											(user.isAdminPanelUser
												? "to exit admin panel view?"
												: "to sign in as admin?"),
										confirmCallback: () =>
											handleSignAsAdmin(),
									});
								}}
							>
								{user.isAdminPanelUser
									? "Continue Onboarding"
									: "Sign in as Admin"}
							</Button>
						)}

						{(user.profileStatus ===
							ProfileStatusMapping.Onboarding ||
							user.profileStatus ===
								ProfileStatusMapping.ChangesRequested) &&
							!user.isAdminPanelUser && (
								<Button
									variant="outline"
									onClick={() => {
										setShowWelcomeModal(true);
									}}
									leftSection={<IconInfoCircle size={16} />}
								>
									About {APP_NAME}
								</Button>
							)}

						<Notifications />

						<Menu
							shadow="md"
							width={240}
							position="bottom-end"
							withArrow
						>
							<Menu.Target>
								<Avatar
									color="blue"
									radius="xl"
									style={{ cursor: "pointer" }}
								>
									<IconUser size={18} />
								</Avatar>
							</Menu.Target>

							<Menu.Dropdown>
								<Box bg="gray.0" px="md" py="xs">
									<Text size="xs" c="dimmed" fw={500}>
										Signed in as
									</Text>
									<Text size="sm" fw={600} truncate>
										{user.email}
									</Text>
								</Box>

								<Menu.Divider />

								<MenuItem
									color="red"
									onClick={() => {
										openCustomModal({
											title: "Are you sure you want to logout?",
											confirmCallback: () =>
												handleLogout(),
										});
									}}
									leftSection={<IconLogout size={16} />}
									style={{
										textAlign: "left",
										fontWeight: 500,
									}}
								>
									Logout
								</MenuItem>
							</Menu.Dropdown>
						</Menu>
					</Group>
				)}
			</Group>
			{showWelcomeModal && (
				<WelcomeModal
					openedModal={showWelcomeModal}
					onClose={() => setShowWelcomeModal(false)}
				/>
			)}
		</AppShell.Header>
	);
}

export default Header;
