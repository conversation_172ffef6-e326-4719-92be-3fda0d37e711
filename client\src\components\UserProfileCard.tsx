import {
	Card,
	Text,
	Group,
	Stack,
	Badge,
	<PERSON>tar,
	<PERSON>,
	Anchor,
	rem,
	Spoiler,
	Grid,
} from "@mantine/core";
import {
	IconHeart,
	IconQuote,
	IconMapPin,
	IconMail,
	IconBrandX,
	IconBrandInstagram,
	IconBrandLinkedin,
	IconNotebook,
	IconWorld,
	IconHome,
	IconHash,
} from "@tabler/icons-react";
import type { ReactNode } from "react";
import { roleLabels, rolesLabelMap } from "../constants";
import { resolveImageUrl } from "../utils/imageUrl";
import EllipsisCell from "./EllipsisCell";

type UserProfileCardProps = {
	firstName: string;
	middleName: string;
	lastName: string;
	email: string;
	role: number;
	image: string;
	address: string;
	city: string;
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	currentOrganization: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
	pincode: string;
};

type InfoFieldProps = {
	icon: ReactNode;
	label?: string;
	value: string;
	isLink?: boolean;
	fieldKey?: string;
};

const InfoItem = ({
	icon,
	label,
	value,
	isLink = false,
	fieldKey,
}: InfoFieldProps) => {
	const displayValue = value || "N/A";
	const SPOILER_FIELDS = ["introduction", "joy"];
	const useSpoiler = fieldKey ? SPOILER_FIELDS.includes(fieldKey) : false;

	return (
		<Group
			align="flex-start"
			gap="sm"
			wrap="nowrap"
			p="sm"
			style={{ backgroundColor: "#f9fafb", borderRadius: rem(8) }}
		>
			<Box mt={2} color="blue.6">
				{icon}
			</Box>
			<Stack gap={2} style={{ flex: 1 }}>
				<Text size="xs" tt="uppercase" c="gray.6" fw={500}>
					{label}
				</Text>
				{isLink && value ? (
					<Anchor
						href={value}
						target="_blank"
						c="blue.7"
						size="sm"
						style={{ wordBreak: "break-word" }}
					>
						{displayValue}
					</Anchor>
				) : useSpoiler ? (
					<Spoiler
						maxHeight={42}
						showLabel="Show more"
						hideLabel="Show less"
						styles={{
							control: {
								fontSize: "12px",
								color: "#2563eb",
								fontWeight: 400,
							},
						}}
					>
						<Text
							size="sm"
							c="gray.9"
							style={{
								whiteSpace: "pre-wrap",
								wordBreak: "break-word",
							}}
						>
							{displayValue}
						</Text>
					</Spoiler>
				) : (
					<Text
						size="sm"
						c="gray.9"
						maw={700}
						style={{
							whiteSpace: "pre-wrap",
							wordBreak: "break-word",
						}}
					>
						{displayValue}
					</Text>
				)}
			</Stack>
		</Group>
	);
};

const UserProfileCard = ({
	firstName,
	middleName,
	lastName,
	email,
	role,
	image,
	address,
	city,
	introduction,
	quote,
	joy,
	contentLinks,
	currentOrganization,
	twitter,
	instagram,
	linkedIn,
	pincode,
	otherSocialHandles = [],
}: UserProfileCardProps) => {
	return (
		<>
			<Grid gutter="lg">
				<Grid.Col span={{ md: 12, lg: 8 }} order={{ md: 2, lg: 1 }}>
					<Stack gap="lg">
						<Card withBorder shadow="sm" radius="md">
							<Stack gap="sm">
								<Group>
									<Text fw={600}>About</Text>
								</Group>
								<InfoItem
									icon={<IconNotebook size={18} />}
									label="Introduction"
									value={introduction}
									fieldKey="introduction"
								/>
								<InfoItem
									icon={<IconMapPin size={18} />}
									label="City"
									value={city}
									fieldKey="city"
								/>
								<InfoItem
									icon={<IconHash size={18} />}
									label="Pincode"
									value={pincode}
									fieldKey="pincode"
								/>
								<InfoItem
									icon={<IconHome size={18} />}
									label="Address"
									value={address}
									fieldKey="address"
								/>
								<InfoItem
									icon={<IconHeart size={18} />}
									label="What brings me joy"
									value={joy}
									fieldKey="joy"
								/>
							</Stack>
						</Card>

						{(email || twitter || instagram || linkedIn) && (
							<Card withBorder shadow="sm" radius="md">
								<Stack gap="sm">
									<Group>
										<Text fw={600}>Contact & Social</Text>
									</Group>
									<InfoItem
										icon={<IconMail size={18} />}
										label="Email"
										value={email}
										fieldKey="email"
									/>
									<InfoItem
										icon={<IconBrandX size={18} />}
										label="Twitter"
										value={twitter}
										isLink
										fieldKey="twitter"
									/>
									<InfoItem
										icon={<IconBrandInstagram size={18} />}
										label="Instagram"
										value={instagram}
										isLink
										fieldKey="instagram"
									/>
									<InfoItem
										icon={<IconBrandLinkedin size={18} />}
										label="LinkedIn"
										value={linkedIn}
										isLink
										fieldKey="linkedIn"
									/>
								</Stack>
							</Card>
						)}

						{contentLinks?.length > 0 && (
							<Card withBorder shadow="sm" radius="md">
								<Stack gap="sm">
									<Group>
										<Text fw={600}>Content Links</Text>
									</Group>
									{contentLinks.map((link, i) => (
										<InfoItem
											key={i}
											icon={<IconWorld size={18} />}
											value={link}
											isLink
											fieldKey="contentLinks"
										/>
									))}
								</Stack>
							</Card>
						)}
						{otherSocialHandles?.length > 0 && (
							<Card withBorder shadow="sm" radius="md">
								<Stack gap="sm">
									<Group>
										<Text fw={600}>
											Other Social Handles
										</Text>
									</Group>
									{otherSocialHandles.map((link, i) => (
										<InfoItem
											key={i}
											icon={<IconWorld size={18} />}
											value={link}
											isLink
											fieldKey="otherSocialHandles"
										/>
									))}
								</Stack>
							</Card>
						)}
					</Stack>
				</Grid.Col>

				<Grid.Col span={{ md: 12, lg: 4 }} order={{ md: 1, lg: 2 }}>
					<Card
						withBorder
						shadow="sm"
						radius="md"
						style={{
							position: "sticky",
							top: 140,
							alignSelf: "start",
						}}
					>
						<Stack align="center" p="lg">
							<Box pos="relative">
								<Avatar
									src={resolveImageUrl(image)}
									size={128}
									radius="xl"
									style={{ border: "4px solid #dbeafe" }}
								>
									{firstName[0]}
								</Avatar>
							</Box>

							<Text
								size="xl"
								fw={700}
								style={{ whiteSpace: "pre-wrap" }}
							>
								<EllipsisCell
									value={`${firstName} ${middleName} ${lastName}`}
									maxWidth={248}
								/>
							</Text>
							{currentOrganization && (
								<Text c="gray.7">
									<EllipsisCell
										value={currentOrganization}
										maxWidth={248}
									/>
								</Text>
							)}
							{role && (
								<Badge
									p="md"
									bdrs="md"
									variant="light"
									color="blue"
									radius="sm"
								>
									{
										rolesLabelMap[
											roleLabels[
												role as keyof typeof roleLabels
											]
										]
									}
								</Badge>
							)}

							{quote && (
								<Box
									mt="md"
									p="md"
									style={{
										backgroundColor: "#f9fafb",
										borderLeft: `4px solid #3b82f6`,
										borderRadius: rem(6),
										width: "300px",
									}}
								>
									<IconQuote
										size={16}
										color="blue"
										style={{ marginBottom: 8 }}
									/>
									<div
										style={{
											maxHeight: "100px",
											overflowY: "auto",
											overflowX: "hidden",
											wordWrap: "break-word",
											whiteSpace: "pre-wrap",
										}}
									>
										<Text size="sm" c="gray.9" fs="italic">
											{quote}
										</Text>
									</div>
								</Box>
							)}
						</Stack>
					</Card>
				</Grid.Col>
			</Grid>
		</>
	);
};

export default UserProfileCard;
