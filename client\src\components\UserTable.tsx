import { Table, ActionIcon, Group, Menu } from "@mantine/core";
import {
	IconTrash,
	IconPencil,
	IconDotsVertical,
	IconUser,
} from "@tabler/icons-react";
import { roleLabels, rolesLabelMap, ProfileStatusMapping } from "../constants";
import type { UserCreation } from "../types";
import openCustomModal from "./modals/CustomModal";
import EllipsisCell from "./EllipsisCell";
import { useNavigate } from "react-router-dom";

interface UserTableprops {
	users: UserCreation[];
	onDelete: (userId: string) => void;
	onEdit: (user: UserCreation) => void;
	currentUserRole: number;
}

const UserTable = ({
	users,
	currentUserRole,
	onDelete,
	onEdit,
}: UserTableprops) => {
	const navigate = useNavigate();
	const canDelete = currentUserRole === 1 || currentUserRole === 2;

	const handleUserView = (userId: string) => () => {
		navigate(`/search/${userId}/Basic Details`);
	};

	return (
		<Table highlightOnHover withTableBorder striped>
			<Table.Thead>
				<Table.Tr>
					<Table.Th>First Name</Table.Th>
					<Table.Th>Last Name</Table.Th>
					<Table.Th>Email</Table.Th>
					<Table.Th>Mobile</Table.Th>
					<Table.Th>Role</Table.Th>
					{canDelete && <Table.Th>Actions</Table.Th>}
				</Table.Tr>
			</Table.Thead>
			<Table.Tbody>
				{users.length > 0 ? (
					users.map(user => (
						<Table.Tr key={user._id}>
							<Table.Td>
								<EllipsisCell
									value={user.firstName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.secondName}
									maxWidth={150}
								/>
							</Table.Td>
							<Table.Td>
								<EllipsisCell
									value={user.email}
									maxWidth={180}
								/>
							</Table.Td>
							<Table.Td>{user.mobile}</Table.Td>
							<Table.Td>
								{
									rolesLabelMap[
										roleLabels[
											user.role as keyof typeof roleLabels
										]
									]
								}
							</Table.Td>
							{canDelete && (
								<Table.Td>
									<Menu
										position="bottom-end"
										shadow="sm"
										styles={{
											dropdown: {
												minWidth: "140px",
												borderRadius:
													"var(--mantine-radius-md)",
											},
											item: {
												padding: "0.25rem",
												color: "var(--mantine-color-gray-8)",
											},
										}}
									>
										<Menu.Target>
											<ActionIcon
												variant="transparent"
												c={"black"}
											>
												<IconDotsVertical size={16} />
											</ActionIcon>
										</Menu.Target>

										<Menu.Dropdown>
											{user.profileStatus !==
												ProfileStatusMapping.Onboarding && (
												<>
													<Menu.Item
														onClick={handleUserView(
															user._id
														)}
													>
														<Group gap={6}>
															<IconUser
																size={14}
															/>
															View
														</Group>
													</Menu.Item>
													<Menu.Divider />
												</>
											)}
											<Menu.Item
												onClick={() => onEdit?.(user)}
											>
												<Group gap={6}>
													<IconPencil size={14} />
													Edit
												</Group>
											</Menu.Item>
											<Menu.Divider />
											<Menu.Item
												onClick={() =>
													openCustomModal({
														title: "Delete User",
														description:
															"Are you sure you want to delete this user?",
														confirmCallback: () =>
															onDelete?.(
																user._id
															),
													})
												}
											>
												<Group gap={6}>
													<IconTrash size={14} />
													Delete
												</Group>
											</Menu.Item>
										</Menu.Dropdown>
									</Menu>
								</Table.Td>
							)}
						</Table.Tr>
					))
				) : (
					<Table.Tr>
						<Table.Td colSpan={6} style={{ textAlign: "center" }}>
							No Users Found.
						</Table.Td>
					</Table.Tr>
				)}
			</Table.Tbody>
		</Table>
	);
};

export default UserTable;
