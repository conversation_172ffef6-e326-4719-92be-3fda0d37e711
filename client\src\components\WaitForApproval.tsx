import { Container, Title, Text, But<PERSON>, Paper, Flex } from "@mantine/core";
import { IconClockHour4 } from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";

const WaitForApproval = () => {
	const { logout } = useAuth();

	const handleLogout = async () => {
		await logout();
	};

	return (
		<Flex justify={"center"} align={"center"} h={"calc(100vh - 90px)"}>
			<Container size="xs">
				<Paper
					withBorder
					shadow="md"
					p={30}
					mt={30}
					radius="md"
					className="!text-center"
				>
					<Flex justify={"center"} align={"center"} mt={20} mb={20}>
						<IconClockHour4 size={48} stroke={1.5} />
					</Flex>
					<Title order={2}>Approval Pending</Title>
					<Text c="dimmed" size="sm" mt="sm">
						Your account has been successfully created and is now
						awaiting approval from the Supermorpheus team.
					</Text>
					<Text c="dimmed" size="sm" mt="sm">
						You will be notified via email once your account has
						been approved or if there are any updates needed. Thank
						you for your patience.
					</Text>
					<Button fullWidth mt="xl" onClick={handleLogout}>
						Logout
					</Button>
				</Paper>
			</Container>
		</Flex>
	);
};

export default WaitForApproval;
