import { modals } from "@mantine/modals";
import { Text } from "@mantine/core";

interface CustomModalProps {
	title?: string;
	description?: string;
	confirmCallback: () => void;
}

export default function openCustomModal({
	title,
	description,
	confirmCallback,
}: CustomModalProps) {
	modals.openConfirmModal({
		title: title ? title : "Are you sure you want to delete?",
		centered: true,
		children: <Text size="sm">{description ?? ""}</Text>,
		labels: { confirm: "Confirm", cancel: "Cancel" },
		onConfirm: confirmCallback,
		trapFocus: false,
	});
}
