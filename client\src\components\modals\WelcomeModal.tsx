import {
	<PERSON>chor,
	<PERSON>ton,
	Flex,
	Group,
	List,
	Modal,
	Stack,
	Text,
	Title,
} from "@mantine/core";
import { ADMIN_EMAIL, PUBLIC_CONTACT_NAME } from "../../constants";
import { useAuth } from "../../contexts/AuthContext";
import { IconPoint } from "@tabler/icons-react";

type WelcomeModalProps = {
	openedModal: boolean;
	onClose?: () => void;
};

export default function WelcomeModal(props: WelcomeModalProps) {
	const { user } = useAuth();
	return (
		<>
			<Modal
				size={800}
				centered
				opened={props.openedModal}
				title={
					<Title order={3}>Instructions to Build your Profile!</Title>
				}
				closeOnClickOutside={false}
				closeOnEscape={false}
				onClose={() => props.onClose?.()}
				padding={24}
				trapFocus={false}
				withCloseButton={false}
			>
				<Stack gap="md">
					<Stack gap={0}>
						<Text size="md" fw={500} mb="sm">
							Dear {user?.firstName}
						</Text>
						<Text c="dimmed" lh="lg" size="sm">
							Welcome to Gang 360 — a community platform for
							Supermorpheus Gang. It has been designed and built
							as a friendly, secure, and trusted environment for
							gang members to get to know each other with a 360
							degree view of each other’s lives.
						</Text>
					</Stack>

					<Group align="center" gap={4}>
						<Title order={5}>Video First Profiles</Title>
						<List
							spacing="4"
							withPadding
							styles={{
								itemWrapper: {
									alignItems: "flex-start",
								},
							}}
							icon={<IconPoint size={"1rem"} />}
							c="dimmed"
							lh="lg"
							size="sm"
						>
							<List.Item>
								To enable the 360 degree view, each member is
								requested to record three basic videos as their
								introduction to the gang.
							</List.Item>
							<List.Item>
								Each video will be 5-10 minutes long and will
								cover one important aspect of their life. These
								videos will be private and only accessible to
								the SM community members.
							</List.Item>
							<List.Item>
								⁠In our experience and understanding,
								video-based profiles are one of the most natural
								and authentic ways to describe yourself, to
								understand other community members, and further
								build lasting friendships.
							</List.Item>
						</List>
					</Group>

					<Flex align="flex-start" gap="sm">
						<Text c="dimmed" lh="lg" size="sm">
							Please allocate a 20-30 minute slot in your day when
							you can create your profile and record videos in a
							relaxed setting. You can also record different
							videos on different days. If required, you can
							browse through a few sample profiles and videos to
							get an idea.
						</Text>
					</Flex>

					<Flex align="center" gap="sm">
						<Text c="dimmed" size="sm">
							If you have any questions / concerns please get in
							touch with {PUBLIC_CONTACT_NAME} –{" "}
							<Anchor href={`mailto:${ADMIN_EMAIL}`} fw={500}>
								{ADMIN_EMAIL}
							</Anchor>
						</Text>
					</Flex>

					{props.onClose && (
						<Button onClick={props.onClose} fullWidth mt="md">
							Continue Building Profile
						</Button>
					)}
				</Stack>
			</Modal>
		</>
	);
}
