import { useAuth } from "../../contexts/AuthContext";
import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	Textarea,
	ActionIcon,
	Text,
	Flex,
} from "@mantine/core";
import React, { useEffect, useCallback, useRef, useState } from "react";
import { TagsInput } from "@mantine/core";
import { IconPlus, IconTrash, IconX } from "@tabler/icons-react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import type { CurrentLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";

interface CurrentLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: CurrentLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
	onboardingStepCompleted?: boolean;
}

const CurrentLifeForm: React.FC<CurrentLifeProps> = ({
	onFormSuccess,
	lifeData,
	isEditable = true,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	userId,
	onboardingStepCompleted,
}) => {
	const { user } = useAuth();
	const { fetchUser } = useAuth();
	const prevCurrentLifeTagCount = useRef<number>(0);
	const [currentCitiesError, setCurrentCitiesError] = useState<string | null>(
		null
	);
	const [frequentTravelCitiesError, setFrequentTravelCitiesError] = useState<
		string | null
	>(null);
	const [currentLifeTagsError, setCurrentLifeTagsError] = useState<
		string | null
	>(null);
	const form = useForm<CurrentLifeDataType>({
		initialValues: {
			currentLifeSummary: "",
			currentCities: [],
			frequentTravelCities: [],
			currentOrganizations: [],
			currentLifeTags: [],
			videoUrl: "",
		},
		validateInputOnChange: true,
		validateInputOnBlur: true,
		validate: {
			currentLifeSummary: (value: string) => {
				const trimmed = (value || "").trim();
				if (!trimmed) return "Early Life Summary is required";
				const wordCount = trimmed.split(/\s+/).filter(Boolean).length;
				return wordCount <= 100
					? null
					: "Current Life Summary must not exceed 100 words";
			},
			currentLifeTags: (value: string[] | undefined) => {
				if (!value || value.length === 0) return null;
			},
		},
		transformValues: values => ({
			...values,
			currentLifeSummary: values.currentLifeSummary?.trim() || "",
			currentOrganizations: Array.isArray(values.currentOrganizations)
				? values.currentOrganizations.map(org => ({
						name: org?.name?.trim() || "",
						role: org?.role?.trim() || "",
						_id: org._id,
					}))
				: [],
		}),
	});

	useEffect(() => {
		const tags = form.values.currentLifeTags ?? [];
		if (tags.length > 10) {
			if (prevCurrentLifeTagCount.current <= 10) {
				notifications.show({
					title: "Tag Limit Reached",
					message: "You can only add upto 10 tags.",
					color: "red",
					icon: <IconX />,
				});
			}
			form.setFieldValue("currentLifeTags", tags.slice(0, 10));
		}

		prevCurrentLifeTagCount.current = tags.length;
	}, [form, form.values.currentLifeTags]);

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<CurrentLifeDataType>(
				"/api/lifeData/currentLife"
			);
			const data = response.data;

			const fetchedData = {
				...data,
				currentOrganizations:
					(data?.currentOrganizations ?? []).length > 0
						? data.currentOrganizations
						: isEditable
							? [{ name: "", role: "" }]
							: [],
			};
			form.setValues(fetchedData);
			form.setInitialValues(fetchedData);
		} catch (err) {
			console.error(err);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				currentOrganizations:
					(lifeData.currentOrganizations?.length ?? 0) > 0
						? lifeData.currentOrganizations
						: isEditable
							? [{ name: "", role: "" }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, isEditable, fetchData]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const addOrganization = () => {
		form.insertListItem("currentOrganizations", { name: "", role: "" });
	};

	const removeOrganization = (index: number) => {
		if (form.values.currentOrganizations.length <= 1) return;
		form.removeListItem("currentOrganizations", index);
	};

	const handleTrimBlur = useCallback(
		(fieldName: string) =>
			(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
				form.setFieldValue(fieldName, e.target.value.trim());
			},
		[form]
	);

	const handleTagsChange = useCallback(
		(fieldName: string) => (tags: string[]) => {
			const trimmed = tags
				.map(tag => tag.trim())
				.filter(tag => tag.length > 0);

			form.setFieldValue(fieldName, trimmed);
		},
		[form]
	);

	const handleSubmit = async (values: CurrentLifeDataType) => {
		if (!form.isDirty() && user && user.onboardingStepCompleted) {
			notifications.show({
				title: "You’re all set",
				message: "No new changes detected since your last save.",
				color: "orange",
			});
			setEditing?.(false);
			return;
		}
		const summaryWordCount = values.currentLifeSummary
			? values.currentLifeSummary.trim().split(/\s+/).length
			: 0;
		if (summaryWordCount > 100) {
			notifications.show({
				title: "Validation Error",
				message: "Summary must be 100 words or fewer.",
				color: "red",
			});
			return;
		}

		const currentLife = {
			...values,
			currentOrganizations: values.currentOrganizations.map(org => ({
				...org,
				name: org?.name?.trim() ?? "",
				role: org?.role?.trim() ?? "",
			})),
		};

		try {
			const response = await apiClient.post(
				"/api/lifeData/update",
				{
					currentLife,
					userId,
				},
				{ withCredentials: true }
			);

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});

			form.setInitialValues(currentLife);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};

	if (!form.values) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="CurrentLife"
				onboardingStepCompleted={onboardingStepCompleted}
				userId={userId}
			/>

			<Title order={2} size="h2" mb="xl">
				Current Life Details
			</Title>
			<form>
				<Stack>
					<Stack gap={0}>
						<Textarea
							label="Current Life Summary"
							description="Max 100 words"
							autosize
							minRows={3}
							disabled={!isEditable}
							{...form.getInputProps("currentLifeSummary")}
							onBlur={handleTrimBlur("currentLifeSummary")}
						/>
					</Stack>

					<Stack gap={4}>
						<TagsInput
							label="Current Cities"
							placeholder="Add a city and press Enter"
							disabled={!isEditable}
							value={form.values.currentCities}
							onChange={tags => {
								if (tags.length > 10) {
									setCurrentCitiesError(
										"You can only add up to 10 cities"
									);
									return;
								}
								setCurrentCitiesError(null);
								handleTagsChange("currentCities")(tags);
							}}
							onDuplicate={() =>
								setCurrentCitiesError(
									"Duplicate cities are not allowed"
								)
							}
							onKeyDown={event => {
								if (
									(event.key === "Backspace" ||
										event.key === "Delete") &&
									currentCitiesError
								) {
									setCurrentCitiesError(null);
								}
							}}
							error={currentCitiesError ? true : false}
						/>
						{currentCitiesError && (
							<Text size="xs" c="red">
								{currentCitiesError}
							</Text>
						)}
					</Stack>

					<Title order={4}>Current Organizations</Title>
					{form.values.currentOrganizations.length > 0 ? (
						form.values.currentOrganizations.map((_, index) => (
							<Group key={index} align="flex-end" gap="xs">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`currentOrganizations.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`currentOrganizations.${index}.name`
									)}
								/>
								<TextInput
									label="Role"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`currentOrganizations.${index}.role`
									)}
									onBlur={handleTrimBlur(
										`currentOrganizations.${index}.role`
									)}
								/>
								{isEditable && (
									<ActionIcon
										color="red"
										variant="subtle"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeOrganization(index),
											});
										}}
										disabled={
											form.values.currentOrganizations
												.length === 1
										}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No organization to display.
						</Text>
					)}
					{isEditable && (
						<Button
							variant="light"
							leftSection={<IconPlus size={14} />}
							onClick={addOrganization}
						>
							Add Organization
						</Button>
					)}

					<Stack gap={4}>
						<TagsInput
							label="Frequent Travel Cities"
							placeholder="Add a city and press Enter"
							disabled={!isEditable}
							value={form.values.frequentTravelCities}
							onChange={tags => {
								if (tags.length > 10) {
									setFrequentTravelCitiesError(
										"You can only add up to 10 cities"
									);
									return;
								}
								setFrequentTravelCitiesError(null);
								handleTagsChange("frequentTravelCities")(tags);
							}}
							onDuplicate={() =>
								setFrequentTravelCitiesError(
									"Duplicate cities are not allowed"
								)
							}
							onKeyDown={event => {
								if (
									(event.key === "Backspace" ||
										event.key === "Delete") &&
									frequentTravelCitiesError
								) {
									setFrequentTravelCitiesError(null);
								}
							}}
							error={frequentTravelCitiesError ? true : false}
						/>
						{frequentTravelCitiesError && (
							<Text size="xs" c="red">
								{frequentTravelCitiesError}
							</Text>
						)}
					</Stack>
					<Stack gap={4}>
						<TagsInput
							label="Current Life Tags"
							placeholder={
								isEditable ? "Add a tag and press Enter" : ""
							}
							value={form.values.currentLifeTags}
							disabled={!isEditable}
							onChange={tags => {
								if (tags.length > 10) {
									setCurrentLifeTagsError(
										"You can only add up to 10 tags"
									);
									return;
								}
								setCurrentLifeTagsError(null);
								handleTagsChange("currentLifeTags")(tags);
							}}
							onDuplicate={() =>
								setCurrentLifeTagsError(
									"Duplicate tags are not allowed"
								)
							}
							onKeyDown={event => {
								if (
									(event.key === "Backspace" ||
										event.key === "Delete") &&
									currentLifeTagsError
								) {
									setCurrentLifeTagsError(null);
								}
							}}
							error={currentLifeTagsError ? true : false}
						/>
						{currentLifeTagsError && (
							<Text size="xs" c="red">
								{currentLifeTagsError}
							</Text>
						)}
					</Stack>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={() => form.onSubmit(handleSubmit)()}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default CurrentLifeForm;
