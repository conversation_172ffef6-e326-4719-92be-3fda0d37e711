import {
	TextInput,
	Paper,
	Title,
	Button,
	Stack,
	Group,
	ActionIcon,
	Text,
	Flex,
	Textarea,
} from "@mantine/core";
import { TagsInput } from "@mantine/core";
import React, { useCallback, useEffect, useState } from "react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { IconTrash } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import type { EarlyLifeDataType } from "../../types";
import { useForm } from "@mantine/form";
import openCustomModal from "../modals/CustomModal";
import FullScreenLoader from "../FullScreenLoader";
import { useAuth } from "../../contexts/AuthContext";

interface EarlyLifeProps {
	onFormSuccess?: () => void;
	isEditable?: boolean;
	lifeData: EarlyLifeDataType | null;
	setHasUnsavedChanges?: (hasChanges: boolean) => void;
	setResetForm?: (resetFunc: () => void) => void;
	setEditing?: (value: boolean) => void;
	editing?: boolean;
	fetchProfile?: () => void;
	userId?: string;
	onboardingStepCompleted: boolean;
}

const EarlyLifeForm: React.FC<EarlyLifeProps> = ({
	onFormSuccess,
	lifeData,
	setHasUnsavedChanges,
	setResetForm,
	setEditing,
	editing,
	fetchProfile,
	isEditable = true,
	userId,
	onboardingStepCompleted,
}) => {
	const { user, fetchUser } = useAuth();
	const [tagsError, setTagsError] = useState<string | null>(null);

	const form = useForm<EarlyLifeDataType>({
		initialValues: {
			birthCity: "",
			hometownCity: "",
			schools: [],
			universities: [],
			earlyLifeTags: [],
			videoUrl: "",
			earlyLifeSummary: "",
		},
		validateInputOnChange: true,
		validateInputOnBlur: true,
		validate: {
			earlyLifeSummary: (value: string) => {
				const trimmed = (value || "").trim();
				if (!trimmed) return "Early Life Summary is required";
				const wordCount = trimmed.split(/\s+/).filter(Boolean).length;
				return wordCount <= 100
					? null
					: "Early Life Summary must not exceed 100 words";
			},
			earlyLifeTags: (value: string[] | undefined) => {
				if (!value || value.length === 0) return null;
				return value.length < 10
					? null
					: "You can only add up to 10 tags";
			},
		},
		transformValues: values => ({
			...values,
			earlyLifeSummary: values.earlyLifeSummary?.trim() || "",
			birthCity: values.birthCity?.trim() || "",
			hometownCity: values.hometownCity?.trim() || "",
			schools: Array.isArray(values.schools)
				? values.schools.map(school => ({
						name: school?.name?.trim() || "",
						location: school?.location?.trim() || "",
						_id: school._id,
					}))
				: [],
			universities: Array.isArray(values.universities)
				? values.universities.map(university => ({
						name: university?.name?.trim() || "",
						course: university?.course?.trim() || "",
						location: university?.location?.trim() || "",
						_id: university._id,
					}))
				: [],
			earlyLifeTags: Array.isArray(values.earlyLifeTags)
				? values.earlyLifeTags.map(tag => tag?.trim() || "")
				: [],
		}),
	});

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<EarlyLifeDataType>(
				"/api/lifeData/earlyLife"
			);
			const data = {
				...response.data,
				schools:
					response.data.schools.length > 0
						? response.data.schools
						: !isEditable
							? []
							: [{ name: "", location: "" }],
				universities:
					response.data.universities.length > 0
						? response.data.universities
						: !isEditable
							? []
							: [{ name: "", course: "", location: "" }],
			};
			form.setValues(data);
			form.setInitialValues(data);
		} catch (err) {
			console.error(`Error fetching early life data: ${err}`);
		}
	}, [isEditable]);

	useEffect(() => {
		if (lifeData) {
			const updatedLifeData = {
				...lifeData,
				schools:
					lifeData.schools.length > 0
						? lifeData.schools
						: isEditable
							? [{ name: "", location: "" }]
							: [],
				universities:
					lifeData.universities.length > 0
						? lifeData.universities
						: isEditable
							? [{ name: "", course: "", location: "" }]
							: [],
			};
			form.setValues(updatedLifeData);
			form.setInitialValues(updatedLifeData);
		} else {
			fetchData();
		}
	}, [lifeData, fetchData, isEditable]);

	const resetForm = useCallback(() => {
		form.reset();
	}, [form]);

	useEffect(() => {
		setResetForm?.(resetForm);
	}, [resetForm]);

	useEffect(() => {
		setHasUnsavedChanges?.(form.isDirty());
	}, [form.values]);

	const addSchool = () => {
		form.insertListItem("schools", { name: "", location: "" });
	};

	const removeSchool = (index: number) => {
		if (form.values.schools.length <= 1) return;
		form.removeListItem("schools", index);
	};

	const addUniversity = () => {
		form.insertListItem("universities", {
			name: "",
			course: "",
			location: "",
		});
	};

	const removeUniversity = (index: number) => {
		if (form.values.universities.length <= 1) return;
		form.removeListItem("universities", index);
	};

	const handleTrimBlur = useCallback(
		(fieldName: string) =>
			(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
				form.setFieldValue(fieldName, e.target.value.trim());
			},
		[form]
	);

	const handleTagsChange = useCallback(
		(fieldName: string) => (tags: string[]) => {
			const trimmed = tags.map(tag => tag.trim()).filter(Boolean);
			form.setFieldValue(fieldName, trimmed);
		},
		[form]
	);

	const handleSubmit = async (values: EarlyLifeDataType) => {
		try {
			if (!form.isDirty() && user && user.onboardingStepCompleted) {
				notifications.show({
					title: "You’re all set",
					message: "No new changes detected since your last save.",
					color: "orange",
				});
				setEditing?.(false);
				return;
			}

			const summaryWordCount = values.earlyLifeSummary
				? values.earlyLifeSummary.trim().split(/\s+/).filter(Boolean)
						.length
				: 0;

			if (summaryWordCount > 100) {
				notifications.show({
					title: "Validation Error",
					message: "Summary must be 100 words or fewer.",
					color: "red",
				});
				return;
			}

			const response = await apiClient.post(`/api/lifeData/update`, {
				earlyLife: values,
				userId,
			});

			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			form.setInitialValues(values);
			fetchUser();
			onFormSuccess?.();
			setEditing?.(false);
			fetchProfile?.();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message || "Failed to save data",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to save data",
					color: "red",
				});
			}
		}
	};

	const handleCancel = () => {
		form.reset();
		setEditing?.(false);
	};

	if (!form.values) return <FullScreenLoader />;

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Flex justify={"flex-end"} mb="sm">
				{editing && (
					<Button variant="outline" onClick={handleCancel}>
						Cancel
					</Button>
				)}
			</Flex>
			<VideoPreviewAndUpload
				editing={editing}
				videoPreviewUrl={form.values.videoUrl}
				setHasUnsavedChanges={setHasUnsavedChanges}
				videoType="EarlyLife"
				onboardingStepCompleted={onboardingStepCompleted}
				userId={userId}
			/>

			<Title order={2} size="h2" mb="xl">
				Early Life Details
			</Title>
			<form>
				<Stack>
					<Stack gap={6}>
						<Textarea
							label="Early Life Summary"
							description="Max 100 words"
							autosize
							minRows={3}
							disabled={!isEditable}
							{...form.getInputProps("earlyLifeSummary")}
							onBlur={handleTrimBlur("earlyLifeSummary")}
						/>
					</Stack>

					<TextInput
						label="Birth City"
						disabled={!isEditable}
						{...form.getInputProps("birthCity")}
						onBlur={handleTrimBlur("birthCity")}
					/>
					<TextInput
						label="Hometown City"
						disabled={!isEditable}
						{...form.getInputProps("hometownCity")}
						onBlur={handleTrimBlur("hometownCity")}
					/>

					<Title order={4}>Schools</Title>
					{form.values.schools.length > 0 ? (
						form.values.schools.map((_, index) => (
							<Group key={index} align="end" gap="sm">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`schools.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`schools.${index}.name`
									)}
								/>
								<TextInput
									label="Location"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`schools.${index}.location`
									)}
									onBlur={handleTrimBlur(
										`schools.${index}.location`
									)}
								/>
								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeSchool(index),
											});
										}}
										disabled={
											form.values.schools.length === 1
										}
										style={{ alignSelf: "flex-end" }}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No school to display.
						</Text>
					)}

					{isEditable && (
						<Button variant="light" onClick={addSchool}>
							+ Add School
						</Button>
					)}

					<Title order={4}>Universities</Title>
					{form.values.universities.length > 0 ? (
						form.values.universities.map((_, index) => (
							<Group key={index} align="end" gap="sm">
								<TextInput
									label="Name"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.name`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.name`
									)}
								/>
								<TextInput
									label="Course"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.course`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.course`
									)}
								/>
								<TextInput
									label="Location"
									style={{ flex: 1 }}
									disabled={!isEditable}
									{...form.getInputProps(
										`universities.${index}.location`
									)}
									onBlur={handleTrimBlur(
										`universities.${index}.location`
									)}
								/>
								{isEditable && (
									<ActionIcon
										variant="subtle"
										color="red"
										onClick={() => {
											openCustomModal({
												confirmCallback: () =>
													removeUniversity(index),
											});
										}}
										disabled={
											form.values.universities.length ===
											1
										}
										style={{ alignSelf: "flex-end" }}
									>
										<IconTrash size={16} />
									</ActionIcon>
								)}
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed" className="pl-1">
							No university to display.
						</Text>
					)}

					{isEditable && (
						<Button variant="light" onClick={addUniversity}>
							+ Add University
						</Button>
					)}

					{/* Tags input with inline validation */}
					<div>
						<TagsInput
							label="Early Life Tags"
							description="Max 10 Tags"
							placeholder={
								isEditable ? "Add a tag and press Enter" : ""
							}
							disabled={!isEditable}
							value={form.values.earlyLifeTags}
							onChange={tags => {
								// Limit max 10 tags
								if (tags.length > 10) {
									setTagsError(
										"You can only add up to 10 tags"
									);
									return;
								}
								// Clear error if valid
								setTagsError(null);

								handleTagsChange("earlyLifeTags")(tags);
							}}
							onDuplicate={() => {
								// Set inline error on duplicate
								setTagsError("Duplicate tags are not allowed");
							}}
							onKeyDown={event => {
								// Detect Backspace/Delete to remove error when tag is deleted
								if (
									(event.key === "Backspace" ||
										event.key === "Delete") &&
									form.values.earlyLifeTags.length <= 10 &&
									tagsError
								) {
									setTagsError(null);
								}
							}}
							error={tagsError}
						/>
					</div>

					{isEditable && (
						<Group justify="flex-end">
							<Button
								w={100}
								onClick={() =>
									form.onSubmit(values =>
										handleSubmit(
											values as unknown as EarlyLifeDataType
										)
									)()
								}
							>
								Save
							</Button>
						</Group>
					)}
				</Stack>
			</form>
		</Paper>
	);
};

export default EarlyLifeForm;
