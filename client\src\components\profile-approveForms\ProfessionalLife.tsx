import React, { useState } from "react";
import ProfessionalLifePreview from "../profile/ProfessionalLifePreview";
import type { ProfessionalLifeDataType } from "../../types";
import ProfessionalLifeForm from "../onBoarding-forms/ProfessionalLifeForm";

interface ProfessionalLifeProps {
	professionalLifeData: ProfessionalLifeDataType;
	fetchProfile: () => void;
	userId?: string;
	updatedProfessionalLifeData?: ProfessionalLifeDataType;
	isEditable?: boolean;
	onboardingStepCompleted: boolean;
}

const ProfessionalLife: React.FC<ProfessionalLifeProps> = ({
	professionalLifeData,
	fetchProfile,
	userId,
	updatedProfessionalLifeData,
	isEditable,
	onboardingStepCompleted,
}) => {
	const [editing, setEditing] = useState(false);

	return (
		<>
			{editing ? (
				<>
					<ProfessionalLifeForm
						fetchProfile={fetchProfile}
						editing={editing}
						setEditing={setEditing}
						lifeData={
							updatedProfessionalLifeData ?? professionalLifeData
						}
						userId={userId}
						onboardingStepCompleted={onboardingStepCompleted}
					/>
				</>
			) : (
				<ProfessionalLifePreview
					showEdit={isEditable ?? true}
					setEditing={setEditing}
					lifeData={professionalLifeData}
					updatedLifeData={updatedProfessionalLifeData}
					userId={userId}
				/>
			)}
		</>
	);
};

export default ProfessionalLife;
