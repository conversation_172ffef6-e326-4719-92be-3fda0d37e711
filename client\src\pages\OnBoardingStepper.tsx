import {
	Step<PERSON>,
	<PERSON>,
	Modal,
	<PERSON><PERSON>,
	Group,
	Text,
	Container,
	Box,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { useEffect, useState, useRef, useCallback, useTransition } from "react";
import EarlyLifeForm from "../components/onBoarding-forms/EarlyLifeForm";
import ProfessionalLifeForm from "../components/onBoarding-forms/ProfessionalLifeForm";
import CurrentLifeForm from "../components/onBoarding-forms/CurrentLifeForm";
import apiClient from "../config/axios";
import type { AllLifeDataType } from "../types";
import { useNavigate, useParams } from "react-router-dom";
import FullScreenLoader from "../components/FullScreenLoader";
import VideoUpload from "../components/VideoUpload";
import { useAuth } from "../contexts/AuthContext";
import Profile from "./Profile";
import { IconCheck } from "@tabler/icons-react";
import ProfileTabsWrapper from "../components/ProfileTabsWrapper";
import WelcomeModal from "../components/modals/WelcomeModal";
import { ONBOARDING_STEP, ProfileStatusMapping } from "../constants";
import { useNotifications } from "../contexts/NotificationContext";
import { getStepperColors } from "../utils";

type StepsType = {
	label: string;
	description: string;
	param: string;
};

const STEPS: StepsType[] = [
	{ label: "Step 1", description: "Basic Details", param: "basic-details" },
	{
		label: "Step 2",
		description: "Early Life Video",
		param: "earlyLifeVideo",
	},
	{
		label: "Step 3",
		description: "Early Life Details",
		param: "earlyLifeDetails",
	},
	{
		label: "Step 4",
		description: "Professional Life Video",
		param: "professionalLifeVideo",
	},
	{
		label: "Step 5",
		description: "Professional Life Details",
		param: "professionalLifeDetails",
	},
	{
		label: "Step 6",
		description: "Current Life Video",
		param: "currentLifeVideo",
	},
	{
		label: "Step 7",
		description: "Current Life Details",
		param: "currentLifeDetails",
	},
	{ label: "Step 8", description: "Submit for Review", param: "finalStep" },
];

function OnBoardingStepper() {
	const { user, fetchUser } = useAuth();
	const { actionInfo, getActionInfo } = useNotifications();
	const { step } = useParams();
	const navigate = useNavigate();
	const [isPending, startTransition] = useTransition();
	const [activeStep, setActiveStep] = useState<number>(() => {
		const storedActiveStep = sessionStorage.getItem("activeStep");
		return storedActiveStep ? JSON.parse(storedActiveStep) : 0;
	});
	const [lifeData, setLifeData] = useState<AllLifeDataType | null>(null);
	const [loading, setLoading] = useState<boolean>(true);
	const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [nextStep, setNextStep] = useState<number>(0);
	const [completedSteps, setCompletedSteps] = useState<Set<number>>(
		new Set<number>()
	);
	const [highestStepVisited, setHighestStepVisited] = useState<number>(0);
	const [showWelcomeModal, setShowWelcomeModal] = useState<boolean>(false);
	const [isStepBusy, setIsStepBusy] = useState<boolean>(() => {
		const storedIsBusy = sessionStorage.getItem("isStepBusy");
		return storedIsBusy ? JSON.parse(storedIsBusy) : false;
	});

	const formResetRef = useRef<(() => void) | null>(null);

	useEffect(() => {
		sessionStorage.setItem("activeStep", JSON.stringify(activeStep));
	}, [activeStep]);

	useEffect(() => {
		sessionStorage.setItem("isStepBusy", JSON.stringify(isStepBusy));
	}, [isStepBusy]);

	useEffect(() => {
		if (isStepBusy) return;
		if (!user) {
			console.log("User not found");
			return;
		}
		const stepIndex = STEPS.findIndex(stepItem => stepItem.param === step);
		const onboardingStep = user.onboardingStep
			? user.onboardingStep - 1
			: 0;
		if (stepIndex !== -1 && stepIndex <= onboardingStep) {
			setActiveStep(stepIndex);
		} else if (user?.onboardingStep) {
			navigate(`/${STEPS[onboardingStep].param}`);
		}
		if (user?.isFirstLogin) {
			setShowWelcomeModal(true);
		}
	}, [step, navigate, user, isStepBusy]);

	useEffect(() => {
		if (isStepBusy && step !== STEPS[activeStep].param) {
			navigate(`/${STEPS[activeStep].param}`, { replace: true });
			notifications.show({
				title: "Processing Video",
				message:
					"Please wait until the video has been processed before navigating to other steps.",
				color: "yellow",
			});
		}
	}, [step, isStepBusy, activeStep, navigate]);

	const fetchData = useCallback(async () => {
		try {
			const response = await apiClient.get<AllLifeDataType>(
				"/api/lifeData/allLifeData"
			);
			setLifeData(response.data);
		} catch (error) {
			console.log(`Error fetching data: ${error}`);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		const completed = new Set<number>();
		if (user) {
			for (let i = 0; i < user.onboardingStep - 1; i++) {
				completed.add(i);
			}
			setCompletedSteps(completed);
			setHighestStepVisited(prev =>
				Math.max(prev, (user.onboardingStep ?? 1) - 1)
			);
		}
	}, [user]);

	useEffect(() => {
		setHighestStepVisited(prev => Math.max(prev, activeStep));
	}, [activeStep]);

	const getNextStepPath = useCallback((currentStep: number) => {
		const nextStepIndex = currentStep + 1;
		return nextStepIndex < STEPS.length
			? `/${STEPS[nextStepIndex].param}`
			: null;
	}, []);

	const onStepDone = useCallback(async () => {
		try {
			setHasUnsavedChanges(false);

			startTransition(() => {
				setCompletedSteps(prev => new Set([...prev, activeStep]));
			});

			await Promise.all([fetchUser(), fetchData(), getActionInfo()]);

			const nextPath = getNextStepPath(activeStep);
			if (nextPath) {
				navigate(nextPath);
			}
		} catch (error) {
			console.error("Step completion failed:", error);
			setCompletedSteps(prev => {
				const reverted = new Set(prev);
				reverted.delete(activeStep);
				return reverted;
			});
		}
	}, [
		activeStep,
		fetchData,
		fetchUser,
		getActionInfo,
		getNextStepPath,
		navigate,
	]);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	useEffect(() => {
		if (!user) return;
		if (user.profileStatus === ProfileStatusMapping.ChangesRequested)
			return;

		const pollSteps = [1, 3, 5];
		if (!pollSteps.includes(activeStep)) return;

		if (user.onboardingStep - 1 > activeStep) return;

		const intervalId = setInterval(async () => {
			try {
				const prevStep = user.onboardingStep;
				const updatedUser = await fetchUser();
				const newStep = updatedUser?.onboardingStep;
				if (newStep && newStep !== prevStep) {
					await fetchData();
					navigate(`/${STEPS[newStep - 1].param}`);
				}
			} catch (err) {
				console.error("Polling failed:", err);
			}
		}, 10_000);

		return () => clearInterval(intervalId);
	}, [activeStep, fetchUser, fetchData, navigate, user]);

	const handleStepClick = (index: number) => {
		if (isStepBusy) {
			notifications.show({
				title: "Processing Video",
				message:
					"Please wait until the video has been processed before navigating to other steps.",
				color: "yellow",
			});
			return;
		}
		if (index > highestStepVisited) {
			return;
		}
		if (hasUnsavedChanges && index !== activeStep) {
			setIsModalOpen(true);
			setNextStep(index);
		} else {
			navigate(`/${STEPS[index].param}`);
		}
	};

	const handleConfirmNavigation = () => {
		if (formResetRef.current) {
			formResetRef.current();
		}
		setHasUnsavedChanges(false);
		navigate(`/${STEPS[nextStep].param}`);
		setIsModalOpen(false);
	};

	const handleCancelNavigation = () => {
		setIsModalOpen(false);
	};

	useEffect(() => {
		const handleBeforeUnload = (event: BeforeUnloadEvent) => {
			if (hasUnsavedChanges) {
				event.preventDefault();
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
		};
	}, [hasUnsavedChanges, setHasUnsavedChanges]);

	const renderActiveForm = () => {
		if (!user) {
			return <FullScreenLoader />;
		}

		const commonProps = {
			onboardingStepCompleted:
				user.onboardingStep === ONBOARDING_STEP.COMPLETED,
			onFormSuccess: onStepDone,
			setHasUnsavedChanges: setHasUnsavedChanges,
			setResetForm: (resetFunc: () => void) => {
				formResetRef.current = resetFunc;
			},
			userId: user._id,
		};

		const stepComponents = [
			<Profile
				isEditable={true}
				hideCancelButton={true}
				onStepDone={onStepDone}
				setHasUnsavedChanges={setHasUnsavedChanges}
			/>,
			<VideoUpload
				key="EarlyLife"
				videoType="EarlyLife"
				description={"Early Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
				onUploadSuccess={onVideoSuccesHandler}
				setIsStepBusy={setIsStepBusy}
			/>,
			<EarlyLifeForm
				{...commonProps}
				lifeData={lifeData?.earlyLifeData ?? null}
			/>,
			<VideoUpload
				key="ProfessionalLife"
				videoType="ProfessionalLife"
				description={"Professional Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
				onUploadSuccess={onVideoSuccesHandler}
				setIsStepBusy={setIsStepBusy}
			/>,
			<ProfessionalLifeForm
				{...commonProps}
				lifeData={lifeData?.professionalLifeData ?? null}
			/>,
			<VideoUpload
				key="CurrentLife"
				videoType="CurrentLife"
				description={"Current Life Video"}
				setHasUnsavedChanges={setHasUnsavedChanges}
				onUploadSuccess={onVideoSuccesHandler}
				setIsStepBusy={setIsStepBusy}
			/>,
			<CurrentLifeForm
				{...commonProps}
				lifeData={lifeData?.currentLifeData ?? null}
			/>,
			<ProfileTabsWrapper />,
		];

		const activeComponent = stepComponents[activeStep] ?? null;
		if (activeStep === stepComponents.length - 1) {
			return activeComponent;
		}

		return <Box p="xl">{activeComponent}</Box>;
	};

	const onVideoSuccesHandler = async () => {
		await Promise.all([fetchUser(), fetchData(), getActionInfo()]);
		navigate(`/${STEPS[activeStep + 1].param}`);
	};

	if (loading || isPending) {
		return <FullScreenLoader />;
	}

	return (
		<div style={{ display: "flex", height: "calc(100vh - 90px)" }}>
			{/* Sidebar */}
			<div
				style={{
					width: "270px",
					height: "100%",
					borderRight: "1px solid #ccc",
					padding: "1rem",
					paddingBottom: "2rem",
					overflowY: "auto",
				}}
			>
				<Title order={3} style={{ marginBottom: "1rem" }}>
					Onboarding
				</Title>
				<Stepper
					active={activeStep}
					orientation="vertical"
					size="sm"
					onStepClick={handleStepClick}
				>
					{STEPS.map((step, index) => (
						<Stepper.Step
							color={getStepperColors(index, actionInfo)}
							key={index}
							label={step.label}
							description={step.description}
							icon={
								completedSteps.has(index) ? (
									<IconCheck
										size={28}
										color={getStepperColors(
											index,
											actionInfo
										)}
									/>
								) : undefined
							}
							style={{
								cursor:
									isStepBusy || index > highestStepVisited
										? "not-allowed"
										: "pointer",
							}}
						/>
					))}
				</Stepper>
			</div>

			{/* Main Content */}
			<Container
				fluid
				style={{
					flex: 1,
					overflowY: "auto",
				}}
			>
				{/* Dynamic Step Header */}
				<Box p="xl" pb={0}>
					<Title order={2}>
						{STEPS[activeStep]?.label} -{" "}
						{STEPS[activeStep]?.description}
					</Title>
				</Box>

				{/* Render Form */}
				{renderActiveForm()}
			</Container>

			{/* Unsaved Changes Modal */}
			<Modal
				opened={isModalOpen}
				onClose={handleCancelNavigation}
				transitionProps={{ transition: "fade", duration: 200 }}
				centered
				withCloseButton={false}
				trapFocus={false}
			>
				<>
					<Text fw={500} size="lg" mb="xs">
						Save your changes before leaving
					</Text>
					<Text mb="lg">
						You have unsaved changes that will be permanently lost
						if you leave this page.
					</Text>
					<Group justify="flex-end">
						<Button
							variant="default"
							onClick={handleCancelNavigation}
						>
							Stay on page
						</Button>
						<Button
							variant="danger"
							onClick={handleConfirmNavigation}
						>
							Leave without saving
						</Button>
					</Group>
				</>
			</Modal>
			{showWelcomeModal && (
				<WelcomeModal
					openedModal={showWelcomeModal}
					onClose={() => setShowWelcomeModal(false)}
				/>
			)}
		</div>
	);
}

export default OnBoardingStepper;
