import { Con<PERSON><PERSON>, <PERSON>lex, <PERSON>, Tabs, <PERSON><PERSON>, Indicator } from "@mantine/core";
import {
	IconBriefcase,
	IconClock,
	IconHome,
	IconUser,
	IconX,
} from "@tabler/icons-react";
import EarlyLife from "../components/profile-approveForms/EarlyLife";
import ProfessionalLife from "../components/profile-approveForms/ProfessionalLife";
import CurrentLife from "../components/profile-approveForms/CurrentLife";
import apiClient from "../config/axios";
import type { previewApprovedResponseDataType } from "../types";
import { useEffect, useState } from "react";
import { isAxiosError } from "axios";
import { notifications } from "@mantine/notifications";
import { useNavigate, useParams } from "react-router-dom";
import { MessageSquare, Send, UserCheck } from "lucide-react";
import FullScreenLoader from "../components/FullScreenLoader";
import Profile from "./Profile";
import AssignRefererCuratorModal from "../components/modals/AssignRefererCuratorModal";
import type { tabsDataType } from "../types";
import { ONBOARDING_STEP, tabsMap, tabsMapReverse } from "../constants";
import SendFeedbackModal from "../components/Feedbacks/SendFeedbackModal";
import { useAuth } from "../contexts/AuthContext";

const ProfileReview = () => {
	const { user } = useAuth();
	const { userId, tab } = useParams();
	const navigate = useNavigate();
	const [userData, setUserData] =
		useState<previewApprovedResponseDataType | null>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [activeTab, setActiveTab] = useState<tabsDataType | null>(
		"basic-details"
	);
	const [openApprovedModal, setOpenApproveModal] = useState<boolean>(false);
	const [openSendFeedback, setOpenSendFeedback] = useState<boolean>(false);

	const fetchData = async () => {
		try {
			setLoading(true);
			const res = await apiClient.get<previewApprovedResponseDataType>(
				`/api/users/review-user/${userId}?view=profileReviewView`
			);

			const data = res.data;

			// Build array of updated sections
			const updatedSections: { key: string; label: string }[] = [];

			if (data.updatedEarlyLifeData) {
				updatedSections.push({
					key: "early-life",
					label: "Early Life",
				});
			}
			if (data.updatedProfessionalLifeData) {
				updatedSections.push({
					key: "professional-life",
					label: "Professional Life",
				});
			}
			if (data.updatedCurrentLifeData) {
				updatedSections.push({
					key: "current-life",
					label: "Current Life",
				});
			}

			setUserData({ ...data, updatedSections }); // store along with userData
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Failed",
					message:
						err.response?.data?.message ??
						err.message ??
						"Failed to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Failed",
					message: "Failed to fetch Profile Data",
					color: "red",
					icon: <IconX />,
				});
			}
			console.error(err);
			navigate("/pending-profiles");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, []);

	const tabChangeHandler = (newTab: tabsDataType) => {
		if (newTab === activeTab) return;
		navigate(`/pending-profiles/user/${userId}/${tabsMapReverse[newTab]}`);
		setActiveTab(newTab);
	};

	const handleViewFeedbacks = () => {
		navigate(
			`/pending-profiles/user/${userId}/${tabsMapReverse[activeTab as tabsDataType]}/feedbacks`
		);
	};
	useEffect(() => {
		if (!tab) return;
		const newTab = tabsMap[tab as keyof typeof tabsMap] ?? "basic-details";
		if (newTab === activeTab) return;
		navigate(
			`/pending-profiles/user/${userId}/${tabsMapReverse[newTab as tabsDataType]}`
		);
		setActiveTab(newTab as tabsDataType);
	}, []);

	if (loading || !userData) {
		return <FullScreenLoader />;
	}

	const lifeFormsCommonProps = {
		userId: userId,
		fetchProfile: fetchData,
		onboardingStepCompleted:
			userData.onboardingStep === ONBOARDING_STEP.COMPLETED,
		isEditable:
			userData?.onboardingStep >= ONBOARDING_STEP.WAIT_FOR_APPROVAL
				? true
				: false,
	};

	return (
		<Container p="lg" mb="xl">
			<Flex justify="space-between" align="center">
				<Title>Profile Review</Title>
				<Flex gap={16}>
					{userId !== user?._id && (
						<>
							{userData.feedbackCount !== undefined && (
								<Button
									variant="outline"
									leftSection={<MessageSquare size={16} />}
									onClick={() => handleViewFeedbacks()}
								>
									Feedbacks
								</Button>
							)}
							<Button
								leftSection={<Send size={16} />}
								onClick={() => setOpenSendFeedback(true)}
							>
								Send Feedback
							</Button>
						</>
					)}

					{userData.onboardingStep >=
						ONBOARDING_STEP.WAIT_FOR_APPROVAL && (
						<Button
							color="green"
							leftSection={<UserCheck size={16} />}
							onClick={() => setOpenApproveModal(true)}
						>
							{userData.profileStatus === "re-approved"
								? "Re-Approve & Publish Profile"
								: "Approve & Publish Profile"}
						</Button>
					)}
				</Flex>
			</Flex>

			<Tabs
				defaultValue={activeTab}
				onChange={e => tabChangeHandler(e?.valueOf() as tabsDataType)}
				variant="default"
				radius="md"
				mt="md"
			>
				<Tabs.List
					style={{
						position: "sticky",
						top: 90,
						zIndex: 1,
						height: "40px",
						backgroundColor: "white",
					}}
				>
					<Tabs.Tab
						value="basic-details"
						leftSection={<IconUser size={16} />}
					>
						Basic Details
					</Tabs.Tab>
					<Tabs.Tab
						value="early-life"
						leftSection={<IconClock size={16} />}
					>
						{userData?.updatedEarlyLifeData ? (
							<Indicator
								zIndex={1}
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<> Early Life Details</>
							</Indicator>
						) : (
							<> Early Life Details</>
						)}
					</Tabs.Tab>
					<Tabs.Tab
						value="professional-life"
						leftSection={<IconBriefcase size={16} />}
					>
						{userData?.updatedProfessionalLifeData ? (
							<Indicator
								zIndex={1}
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<>Professional Life Details</>
							</Indicator>
						) : (
							<>Professional Life Details</>
						)}
					</Tabs.Tab>
					<Tabs.Tab
						value="current-life"
						leftSection={<IconHome size={16} />}
					>
						{userData?.updatedCurrentLifeData ? (
							<Indicator
								zIndex={1}
								size={8}
								color="red"
								offset={-4}
								position="top-end"
							>
								<>Current Life Details</>
							</Indicator>
						) : (
							<>Current Life Details</>
						)}
					</Tabs.Tab>
				</Tabs.List>

				<Tabs.Panel value="basic-details" pt="lg">
					<Profile
						initialValues={{
							firstName: userData.basicDetails.firstName || "",
							middleName: userData.basicDetails.middleName || "",
							secondName: userData.basicDetails.secondName || "",
							email: userData.basicDetails.email,
							image: userData.basicDetails.image ?? null,
							mobile: userData.basicDetails.mobile,
							role: userData.basicDetails.role,
							address: userData.basicDetails.address || "",
							city: userData.basicDetails.city || "",
							currentOrganization:
								userData.basicDetails.currentOrganization || "",
							introduction:
								userData.basicDetails.introduction || "",
							quote: userData.basicDetails.quote || "",
							joy: userData.basicDetails.joy || "",
							twitter: userData.basicDetails.twitter || "",
							instagram: userData.basicDetails.instagram || "",
							linkedIn: userData.basicDetails.linkedIn || "",
							contentLinks:
								userData.basicDetails.contentLinks || [],
							otherSocialHandles:
								userData.basicDetails.otherSocialHandles || [],
							pincode: userData.basicDetails.pincode || "",
						}}
						saveUrl={`/api/users/update-profile/${userId}`}
						fetchProfile={fetchData}
						noEditInProfile={
							userData.onboardingStep >=
							ONBOARDING_STEP.WAIT_FOR_APPROVAL
								? false
								: true
						}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="early-life" pt="lg">
					<EarlyLife
						{...lifeFormsCommonProps}
						earlyLifeData={userData.earlyLifeData}
						updatedEarlyLifeData={userData.updatedEarlyLifeData}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="professional-life" pt="lg">
					<ProfessionalLife
						{...lifeFormsCommonProps}
						professionalLifeData={userData.professionalLifeData}
						updatedProfessionalLifeData={
							userData.updatedProfessionalLifeData
						}
					/>
				</Tabs.Panel>
				<Tabs.Panel value="current-life" pt="lg">
					<CurrentLife
						{...lifeFormsCommonProps}
						currentLifeData={userData.currentLifeData}
						updatedCurrentLifeData={userData.updatedCurrentLifeData}
					/>
				</Tabs.Panel>
			</Tabs>

			{openApprovedModal && (
				<AssignRefererCuratorModal
					opened={openApprovedModal}
					onClose={() => setOpenApproveModal(false)}
					userId={userId}
					onConfirm={() => navigate("/pending-profiles")}
					isReApproved={userData.profileStatus === "re-approved"}
				/>
			)}

			{openSendFeedback && (
				<SendFeedbackModal
					opened={openSendFeedback}
					onClose={() => setOpenSendFeedback(false)}
					userId={userId}
					profileStatus={userData.profileStatus}
					activeTab={activeTab}
					updatedSections={userData.updatedSections ?? []}
				/>
			)}
		</Container>
	);
};

export default ProfileReview;
