import mongoose from "mongoose";

const videoUploadSchema = new mongoose.Schema(
	{
		transcriptionStatus: {
			type: String,
			enum: ["pending", "processing", "completed", "failed"],
			default: "pending",
		},
		thumbnailStatus: {
			type: String,
			enum: ["pending", "processing", "completed", "failed"],
			default: "pending",
		},
		errorMessage: {
			type: String,
		},
		thumbnailErrorMessage: {
			type: String,
		},
		videoType: {
			type: String,
			enum: ["EarlyLife", "ProfessionalLife", "CurrentLife"],
			required: true,
		},
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			ref: "User",
			required: true,
		},
		transcription: {
			type: String,
		},
		thumbnailKeys: {
			type: [String],
			default: [],
		},
		selectedThumbnailKey: {
			type: String,
			default: null,
		},
		retryCount: {
			type: Number,
			default: 0,
		},
		thumbnailRetryCount: {
			type: Number,
			default: 0,
		},
	},
	{
		timestamps: true, // Adds createdAt and updatedAt fields
	}
);

const VideoUpload = mongoose.model("VideoUpload", videoUploadSchema);

export default VideoUpload;
