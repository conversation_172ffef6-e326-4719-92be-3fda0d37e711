{"name": "src", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "nodemon": "nodemon index.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "seed:superAdmin": "node scripts/createSuperAdmin.js", "migration": "node migration/index.js", "format": "prettier --write \"./**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"./**/*.{js,jsx,ts,tsx,json,css,md}\"", "precommit": "lint-staged"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/s3-request-presigner": "^3.858.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.1.0", "express": "^5.1.0", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "fluent-ffmpeg": "^2.1.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "multer": "^2.0.2", "nodemailer": "^7.0.5", "openai": "^5.10.1", "zod": "^3.25.76"}, "devDependencies": {"eslint": "^9.32.0", "eslint-plugin-prettier": "^5.5.3", "lint-staged": "^16.1.4", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "prettier": "^3.6.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write", "prettier --check"], "*.{json,css,md}": ["prettier --write", "prettier --check"]}}