import OpenAI from "openai";
import { APP_CONFIG } from "../config/env.js";
import User from "../models/User.js";
import { USER_FIELD_MAP, videoTypeNumToStr } from "../constants/index.js";

const openai = new OpenAI({
	apiKey: APP_CONFIG.OPENAI_API_KEY,
});

const getPromptAndFields = (videoType, transcriptionText) => {
	let prompt = "";
	let fieldsToExtract = [];
	let logMessage = "";
	let errorContext = "";

	const emptyValueInstruction = `If the transcription text does not contain information for a specific field, use a default empty value. For fields of type String, use "". For fields of type Array, use an empty array []. For object types, return the object with its properties set to their default empty values (e.g., { companyName: "", roles: [] }).`;

	const earlyLifeTagsInstruction = `
From the transcription, extract up to 10 concise tags that represent the person's life during the described period.
Tags may include:
- Cities or places they lived in
- Sports they played
- Areas of interest or study
- Hobbies or leisure activities
- Significant events or themes
Use short, clear phrases (1–3 words each), no duplicates, and no extra commentary.
`;

	const professionalLifeTagsInstruction = `
From the transcription, extract up to 10 concise tags that represent the person's professional life. 
Include:
- Sectors or industries they worked in
- Their specialisations or areas of expertise
- Skills they learned or developed
- Favourite or notable projects
- Tools, technologies, or methods they used
Use short, clear phrases (1–3 words each), avoid duplicates, and no extra commentary.
`;

	const currentLifeTags = `
From the transcription, extract up to 10 concise tags that represent the person’s current life. 
Tags should highlight key aspects such as:
- Cities or regions they currently live in or travel to frequently
- Organizations or workplaces they are part of
- Their current roles or responsibilities
- Skills they have or are developing
- Tools, technologies, or methods they currently use
- Major themes, projects, or areas of focus in their present life
Use short phrases (1–3 words each), avoid duplicates, and do not add extra commentary.
`;

	if (videoType === videoTypeNumToStr[1]) {
		logMessage =
			"Sending transcription to OpenAI for early life data extraction...";
		errorContext = "early life";
		prompt = `Extract the following information from the provided text and format it as a JSON object. If a field is not explicitly mentioned, omit it. ${emptyValueInstruction} Ensure the earlyLifeSummary does not exceed 100 words. ${earlyLifeTagsInstruction}
            birthCity: { type: String },
            hometownCity: { type: String },
            schools: [{ name: { type: String }, location: { type: String } }],
            universities: [{ name: { type: String }, course: { type: String }, location: { type: String } }],
            earlyLifeTags: { type: [String]},
						earlyLifeSummary: { type: String },

            Transcription: "${transcriptionText}"

            Respond with only the JSON object.`;
		fieldsToExtract = [
			"birthCity",
			"hometownCity",
			"schools",
			"universities",
			"earlyLifeTags",
			"earlyLifeSummary",
		];
	} else if (videoType === videoTypeNumToStr[2]) {
		logMessage =
			"Sending transcription to OpenAI for professional life data extraction...";
		errorContext = "professional life";
		prompt = `Extract the following information from the provided text and format it as a JSON object. When extracting roles, normalize them into valid professional job titles. Ensure roles sound like proper job designations (Analyst, Manager, Associate, Specialist, Executive, etc.) rather than just functional areas. If a field is not explicitly mentioned, omit it. ${emptyValueInstruction} Ensure the professionalLifeSummary does not exceed 100 words. ${professionalLifeTagsInstruction}
            firstJob: { companyName: { type: String }, roles: { type: [String] } },
            subsequentJobs: [{ companyName: { type: String }, roles: { type: [String] } }],
            professionalLifeTags: { type: [String] },
						professionalLifeSummary: { type: String },

            Transcription: "${transcriptionText}"

            Respond with only the JSON object.`;
		fieldsToExtract = [
			"firstJob",
			"subsequentJobs",
			"professionalLifeTags",
			"professionalLifeSummary",
		];
	} else if (videoType === videoTypeNumToStr[3]) {
		logMessage =
			"Sending transcription to OpenAI for current life data extraction...";
		errorContext = "current life";
		prompt = `Extract the following information from the provided text and format it as a JSON object. If a field is not explicitly mentioned, omit it. ${emptyValueInstruction} Ensure the currentLifeSummary does not exceed 100 words. ${currentLifeTags}
            currentLifeSummary: { type: String },
            currentCities: { type: [String] },
            currentOrganizations: [{ name: { type: String }, role: { type: String } }],
            frequentTravelCities: { type: [String] },
            currentLifeTags: { type: [String]},
						skills: { type: [String] },

            Transcription: "${transcriptionText}"

            Respond with only the JSON object.`;
		fieldsToExtract = [
			"currentLifeSummary",
			"currentCities",
			"currentOrganizations",
			"frequentTravelCities",
			"currentLifeTags",
		];
	}

	return { prompt, fieldsToExtract, logMessage, errorContext };
};

export const extractDataAndUpdateUser = async (
	transcriptionText,
	videoType,
	userId
) => {
	const { prompt, fieldsToExtract, logMessage, errorContext } =
		getPromptAndFields(videoType, transcriptionText);

	if (!prompt) {
		console.log(`No data extraction logic for video type: ${videoType}`);
		return null;
	}

	try {
		console.log(logMessage);

		const response = await openai.chat.completions.create({
			model: "gpt-5-nano",
			messages: [{ role: "user", content: prompt }],
			response_format: { type: "json_object" },
		});

		let extractedData;
		try {
			console.log("Total Token Used: ", response.usage?.total_tokens);
			const data = JSON.parse(response.choices[0].message.content);
			const tagKeys = [
				"earlyLifeTags",
				"professionalLifeTags",
				"currentLifeTags",
			];

			for (const key of tagKeys) {
				if (data[key]?.type) {
					data[key] = data[key].type;
				}
			}

			extractedData = data;
			console.log(`Extracted data from OpenAI:`, extractedData);
		} catch (parseError) {
			console.error(
				`Error parsing OpenAI response JSON for ${errorContext}:`,
				parseError
			);
			throw new Error(
				`Failed to parse OpenAI JSON for ${errorContext}.`,
				parseError
			);
		}

		const updateData = {};
		for (const field of fieldsToExtract) {
			const value = extractedData[field];
			if (value !== undefined && value !== null) {
				updateData[field] = value;
			}
		}

		if (Object.keys(updateData).length > 0) {
			const user = await User.findById(userId);
			if (user) {
				const stepKey = "onboardingLifeData";
				const targetField = USER_FIELD_MAP[videoType]?.[stepKey];
				if (targetField && updateData && user[targetField]) {
					Object.assign(user[targetField], updateData);
					await user.save();
					console.log(
						`Updated user ${userId} with ${errorContext} info.`
					);
				} else {
					console.log(
						`No matching field found for video type ${videoType} for step ${stepKey} and targetField ${targetField}. with updated data ${JSON.stringify(
							updateData
						)}`
					);
				}
			} else {
				console.log(`User not found with ID: ${userId}`);
			}
		} else {
			console.log(
				`No relevant ${errorContext} data extracted to update.`
			);
		}
		return extractedData;
	} catch (openAICommunicationError) {
		console.error(
			`Error processing ${errorContext} data with OpenAI:`,
			openAICommunicationError
		);
		throw new Error(
			`Error processing ${errorContext} data with OpenAI.`,
			error
		);
	}
};
